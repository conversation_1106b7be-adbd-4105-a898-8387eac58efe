ignore:
  - '**/__init__.py'
  - .git/
  - .venv/
  - node_modules/
  - __pycache__/
  - frontend/node_modules/
  - frontend-vite/node_modules/
  - test-frontend/node_modules/
  - frontend/assets/
  - frontend/styles/
  - frontend-vite/assets/
  - frontend-vite/styles/
  - '**/*.test.tsx'
  - '**/*.spec.ts'
  - '**/*.stories.tsx'
  - '**/__mocks__/**'
  - '**/*.svg'
  - '**/*.png'
  - '**/*.ico'
  - '**/*.css'
  - '**/*.scss'
  - '**/*.lock'
  - '**/*.egg-info/'
  - '.augmentignore'
  - '.augment_guidlines'
  - '.env'
  - '.env.example'
  - '.gitignore'
  - 'Public/'
