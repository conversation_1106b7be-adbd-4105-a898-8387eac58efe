# Technology Stack Analysis

**Last Updated:** 2025-07-09 10:02:58  
**Project:** Jo<PERSON>aDe_Dev  
**Project Path:** C:\Users\<USER>\Documents\VSCode\Jomade\JoMaDe_Dev

---

## 📋 Summary

This document provides a comprehensive analysis of the technology stack used in this project. The information below was automatically detected by scanning project files, dependencies, and configuration.

## 💻 Programming Languages

- **Python**

## 🎨 Frontend Frameworks & Libraries

- **Static HTML**

## ⚙️ Backend Frameworks & Libraries

- **FastAPI**
- **Firecrawl**
- **OpenAI**
- **Uvicorn**

## 🔧 Build Tools & Development

- **npm**
- **pip**

## 🤖 Automation & DevOps Tools

- **AutoHotkey**
- **PowerShell**

## 🔌 Other Technologies

- **Environment Config**
- **Markdown Documentation**

## 📁 Project Structure Analysis

### Frontend Directories
- `frontend/`

### Backend Directories
- `backend/`

---

## 📝 Notes

This analysis was automatically generated by Vibe Architect's techstack detection system. The information above reflects the current state of the project based on:

- **Package files**: `package.json`, `requirements.txt`, `Gemfile`, etc.
- **Configuration files**: Build tools, framework configs, environment files
- **Project structure**: Directory names and file extensions
- **Dependency analysis**: Direct and development dependencies

For manual customization of this techstack information, use the Vibe Architect interface or edit the `techstack.yaml` configuration file.

*Generated by Vibe Architect Techstack Analyzer*
