# JoMaDe Environment Configuration Template
# Copy this file to .env and fill in your actual API keys

# API Configuration
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Application Settings
API_BASE_URL=http://localhost:8000
FRONTEND_PORT=3000
BACKEND_PORT=8000

# Scraper Configuration
CRAWL_LIMIT=10
SCRAPER_TIMEOUT=300
OPENAI_MODEL=gpt-4o-mini
OPENAI_TEMPERATURE=0.0

# Development Settings
DEBUG=false
LOG_LEVEL=INFO
