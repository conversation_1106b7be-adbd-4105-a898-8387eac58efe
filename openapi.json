{"openapi": "3.1.0", "info": {"title": "JoMaDe API", "description": "Job Market Detector API - Simplified Version", "version": "0.2.0"}, "paths": {"/": {"get": {"summary": "Root", "description": "Root endpoint", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/urls": {"get": {"summary": "Get Urls", "description": "Get all job URLs", "operationId": "get_urls_api_urls_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}, "post": {"summary": "Add Url", "description": "Add a new job URL", "operationId": "add_url_api_urls_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Url Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/urls/{url_id}": {"delete": {"summary": "Delete Url", "description": "Delete a job URL", "operationId": "delete_url_api_urls__url_id__delete", "parameters": [{"name": "url_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Url Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"summary": "Update Url", "description": "Update a job URL", "operationId": "update_url_api_urls__url_id__put", "parameters": [{"name": "url_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Url Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Url Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/cv": {"get": {"summary": "Get Cv", "description": "Get CV data", "operationId": "get_cv_api_cv_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}, "put": {"summary": "Update Cv", "description": "Update CV data", "operationId": "update_cv_api_cv_put", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Cv Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/jobs": {"get": {"summary": "Get Jobs", "description": "Get jobs with optional filtering\n\nArgs:\n    source: Filter by source prefix (e.g., AAA)\n    shortlisted: Filter by shortlisted status", "operationId": "get_jobs_api_jobs_get", "parameters": [{"name": "source", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source"}}, {"name": "shortlisted", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Shortlisted"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"summary": "Add Job", "description": "Add a new job", "operationId": "add_job_api_jobs_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Job Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/jobs/{job_id}": {"put": {"summary": "Update Job", "description": "Update a job", "operationId": "update_job_api_jobs__job_id__put", "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Job Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Job Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"summary": "Delete Job", "description": "Delete a job", "operationId": "delete_job_api_jobs__job_id__delete", "parameters": [{"name": "job_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Job Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/scrape": {"post": {"summary": "Scrape Jobs", "description": "Trigger job scraping\n\nThis is a simplified version that just returns mock data.\nIn a real implementation, this would call a scraper module.", "operationId": "scrape_jobs_api_scrape_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Health check endpoint", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}