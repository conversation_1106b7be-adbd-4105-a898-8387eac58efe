# JoMaDe - Job Market Detector

JoMaDe is a web-based application for job market analysis and matching. It helps users find relevant job postings based on their CV and skills.

## Features

- Job scraping from various sources
- CV summary import and editing
- File upload for CV and documents
- Embedding files for RAG-based shortlisting
- Shortlisting jobs based on CV match
- Evaluating shortlisted jobs
- Kadoa integration for job scraping (optional)

## Prerequisites

- Python 3.8 or higher
- Node.js 18 or higher
- npm 8 or higher

## Quick Start

### Option 1: Using the run script (recommended)

For PowerShell users:
```
.\run.ps1
```

Note: If you encounter execution policy restrictions in PowerShell, you may need to run:
```
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
.\run.ps1
```

The run script will:
- Check if all required tools are installed
- Set up the backend virtual environment and install dependencies
- Set up the frontend and install dependencies
- Run both the backend and frontend servers
- Open the application in your default browser

### Option 2: Manual setup

#### Backend Setup

1. Navigate to the backend directory:
   ```
   cd JoMaDe/backend
   ```

2. Create a virtual environment:
   ```
   python -m venv .venv
   ```

3. Activate the virtual environment:
   - Windows Command Prompt:
     ```
     .venv\Scripts\activate
     ```
   - Windows PowerShell:
     ```
     .venv\Scripts\Activate.ps1
     ```

4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

#### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd JoMaDe/frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

#### Running the Application Manually

1. Start the backend server:
   ```
   cd backend
   .venv\Scripts\python -m uvicorn api:app --reload --host 0.0.0.0 --port 8000
   ```

2. In a separate terminal, start the frontend server:
   ```
   cd frontend
   npm run dev
   ```

4. Open your browser and navigate to:
   - Frontend: http://localhost:3000
   - API Documentation: http://localhost:8000/docs

## Project Structure

- `backend/`: FastAPI backend application
  - `api/`: API endpoints
  - `core/`: Core functionality
  - `models/`: Database models
  - `services/`: Business logic
  - `utils/`: Utility functions

- `frontend/`: Simple HTML frontend application
  - `index.html`: Main application page
  - `settings.html`: Settings page
  - `404.html`: Error page
  - `package.json`: Minimal dependencies (http-server)

- `data/`: Application data storage
  - `job_urls.json`: Job source URLs
  - `cv.json`: CV summary data
  - `jobs.json`: Scraped job data
  - `shortlist.json`: Shortlisted jobs

## Configuration

- Environment variables are stored in `.env` (create from `.env.example`)
- Required API keys: FIRECRAWL_API_KEY, OPENAI_API_KEY
- Backend uses JSON file storage in `data/` directory

## Development

- Backend API documentation is available at http://localhost:8000/docs
- Frontend application is available at http://localhost:3000

## API Endpoints

### Core Endpoints

- `GET /`: Root endpoint with API information
- `GET /health`: Health check with system status

### Job URLs Management

- `GET /api/urls`: Get all job URLs
- `POST /api/urls`: Add a new job URL
- `PUT /api/urls/{url_id}`: Update a job URL
- `DELETE /api/urls/{url_id}`: Delete a job URL

### CV Management

- `GET /api/cv`: Get CV summary
- `POST /api/cv`: Update CV summary

### Job Operations

- `GET /api/jobs`: Get all jobs
- `POST /api/scrape`: Trigger job scraping
- `POST /api/shortlist`: Create job shortlist based on CV match
- `GET /api/shortlist`: Get shortlisted jobs

### Real-time Features

- `GET /api/scrape/logs`: Stream real-time scraping logs
- `GET /api/sources/stats`: Get statistics by source

## Workflow

1. Configure job source URLs in the application
2. Add or edit your CV summary
3. Trigger job scraping from configured sources
4. Review scraped jobs and statistics
5. Create shortlist based on CV matching
6. Review and evaluate shortlisted jobs

## Features

- **Real-time Scraping**: Live feedback during job scraping operations
- **AI-Powered Matching**: OpenAI-based CV-job matching with confidence scores
- **Source Management**: Easy addition and management of job source URLs
- **Date-based Caching**: Prevents re-scraping on the same day
- **Statistics Tracking**: Per-source job statistics and scraping history