# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
.venv/
omni_env/
env/
ENV/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js
.next/
out/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db
.cursor/

# Project specific
logs/
*.log
*.db
*.sqlite
*.sqlite3
site-packages/

# Chrome extension
/chrome-extension/