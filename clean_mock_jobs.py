#!/usr/bin/env python3
"""
Script to clean mock jobs from the JoMaDe database.
This script removes all job entries that don't have a 'scraped_at' field,
keeping only legitimate scraped jobs.
"""

import sys
import os
import json
from datetime import datetime

# Add backend directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from storage import JobStore

def main():
    """Clean mock jobs from the database."""
    print("🧹 JoMaDe Mock Jobs Cleaner")
    print("=" * 50)
    
    # Initialize job store (use absolute path)
    data_path = os.path.join(os.path.dirname(__file__), 'data', 'jobs.json')
    jobs_store = JobStore(data_path)
    
    # Get current statistics
    print("📊 Current Database Statistics:")
    stats = jobs_store.get_scraping_stats()
    print(f"   Total jobs: {stats['total_jobs']}")
    print(f"   Real scraped jobs: {stats['scraped_jobs']}")
    print(f"   Mock jobs: {stats['mock_jobs']}")
    print()
    
    if stats['mock_jobs'] == 0:
        print("✅ No mock jobs found. Database is already clean!")
        return
    
    # Show some examples of mock jobs
    mock_jobs = jobs_store.get_mock_jobs()
    print("🔍 Sample Mock Jobs (first 3):")
    for i, job in enumerate(mock_jobs[:3]):
        print(f"   {i+1}. ID: {job.get('id', 'N/A')}")
        print(f"      Title: {job.get('title', 'N/A')}")
        print(f"      Source: {job.get('source', 'N/A')}")
        print(f"      Has scraped_at: {'scraped_at' in job}")
        print()
    
    # Confirm deletion
    response = input(f"❓ Remove {stats['mock_jobs']} mock jobs? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Operation cancelled.")
        return
    
    # Perform cleanup
    print("🗑️  Removing mock jobs...")
    removed_count = jobs_store.remove_mock_jobs()
    
    # Get updated statistics
    new_stats = jobs_store.get_scraping_stats()
    
    print("✅ Cleanup completed!")
    print(f"   Removed: {removed_count} mock jobs")
    print(f"   Remaining: {new_stats['total_jobs']} real scraped jobs")
    print()
    
    # Show source breakdown
    if new_stats.get('jobs_by_source'):
        print("📈 Jobs by Source:")
        for source, count in new_stats['jobs_by_source'].items():
            print(f"   {source}: {count} jobs")
    
    print("\n🎉 Database cleanup successful!")

if __name__ == "__main__":
    main()
