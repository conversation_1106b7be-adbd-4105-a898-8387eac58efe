<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Display Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #url-display {
            font-family: 'Courier New', Consolas, monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e1e5e9;
            overflow-x: auto;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>URL Display Formatting Test</h1>
        <p>This test shows how the URL display will look with the new formatting:</p>
        
        <button class="btn" onclick="loadTestUrls()">Load Test URLs</button>
        <button class="btn" onclick="loadRealUrls()">Load Real URLs</button>
        
        <div id="url-display"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function loadTestUrls() {
            const testData = [
                {
                    prefix: 'AAA',
                    url: 'https://careers.eoexecutives.com/',
                    scraped_jobs: 57,
                    last_scraped: '2025-07-07T13:07:35.241985'
                },
                {
                    prefix: 'AAB',
                    url: 'https://zeppelin.wd3.myworkdayjobs.com/de-DE/careers',
                    scraped_jobs: 23,
                    last_scraped: '2025-07-07T13:05:51.144529'
                },
                {
                    prefix: 'AAC',
                    url: 'https://www.baumann-ag.com/fuer-kandidaten/bewerbungsverfahren/vakanzen/joblist',
                    scraped_jobs: 0,
                    last_scraped: null
                }
            ];
            
            displayUrls(testData);
        }
        
        async function loadRealUrls() {
            try {
                const [urlsResponse, sourceStatsResponse] = await Promise.all([
                    fetch(`${API_BASE}/job-urls`),
                    fetch(`${API_BASE}/api/sources/stats`)
                ]);
                
                if (urlsResponse.ok && sourceStatsResponse.ok) {
                    const urlsData = await urlsResponse.json();
                    const sourceStatsData = await sourceStatsResponse.json();
                    
                    const urls = urlsData.urls || [];
                    const urlData = urlsData.url_data || [];
                    const sourceStats = sourceStatsData.source_stats || {};
                    
                    const formattedData = urls.map((url, index) => {
                        const stats = sourceStats[url] || {
                            scraped_jobs: 0,
                            last_scraped: null
                        };
                        
                        const urlInfo = urlData.find(u => u.url === url);
                        const prefix = urlInfo ? urlInfo.prefix : `AA${String.fromCharCode(65 + index)}`;
                        
                        return {
                            prefix,
                            url,
                            scraped_jobs: stats.scraped_jobs,
                            last_scraped: stats.last_scraped
                        };
                    });
                    
                    displayUrls(formattedData);
                } else {
                    document.getElementById('url-display').textContent = 'Error loading real URLs';
                }
            } catch (error) {
                document.getElementById('url-display').textContent = 'Error: ' + error.message;
            }
        }
        
        function displayUrls(urlData) {
            const formatted = urlData.map(item => {
                const jobCount = item.scraped_jobs || 0;
                const lastScrapedText = item.last_scraped ?
                    ` Last: ${new Date(item.last_scraped).toLocaleDateString()}` :
                    ' Never scraped';
                
                // Format with right-aligned statistics using padding
                const baseText = `${item.prefix}: ${item.url}`;
                const statsText = `(${jobCount} jobs)${lastScrapedText}`;
                
                // Create right-aligned display with proper spacing
                const padding = Math.max(0, 80 - baseText.length);
                const paddedSpaces = ' '.repeat(padding);
                
                return `${baseText}${paddedSpaces}${statsText}`;
            }).join('\n');
            
            document.getElementById('url-display').textContent = formatted;
        }
    </script>
</body>
</html>
