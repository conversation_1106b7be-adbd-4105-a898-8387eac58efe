"""
Simple JSON-based file storage for JoMaDe application.
This replaces the complex markdown-based storage with a simpler JSON approach.
"""

import json
import os
import hashlib
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JsonStore:
    """A simple JSON file-based data store."""
    
    def __init__(self, filename: str):
        """
        Initialize the JSON store.
        
        Args:
            filename: Path to the JSON file
        """
        self.filename = filename
        self.data = []
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        # Load data from file or create empty file
        self.load()
    
    def load(self) -> None:
        """Load data from the JSON file."""
        try:
            if os.path.exists(self.filename):
                with open(self.filename, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                logger.info(f"Loaded {len(self.data)} items from {self.filename}")
            else:
                self.data = []
                self.save()  # Create the file
                logger.info(f"Created new data file: {self.filename}")
        except Exception as e:
            logger.error(f"Error loading data from {self.filename}: {str(e)}")
            self.data = []
            self.save()  # Create the file with empty data
    
    def save(self) -> None:
        """Save data to the JSON file."""
        try:
            with open(self.filename, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, indent=2)
            logger.info(f"Saved {len(self.data)} items to {self.filename}")
        except Exception as e:
            logger.error(f"Error saving data to {self.filename}: {str(e)}")
    
    def get_all(self) -> List[Dict[str, Any]]:
        """Get all items from the store."""
        return self.data
    
    def get_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """Get an item by its ID."""
        for item in self.data:
            if item.get('id') == item_id:
                return item
        return None
    
    def add(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new item to the store.
        
        Args:
            item: The item to add
            
        Returns:
            The added item with generated ID if not provided
        """
        # Generate ID if not provided
        if 'id' not in item:
            item['id'] = str(len(self.data) + 1)
        
        # Add timestamps
        now = datetime.now().isoformat()
        item['created_at'] = now
        item['updated_at'] = now
        
        self.data.append(item)
        self.save()
        return item
    
    def update(self, item_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update an existing item.
        
        Args:
            item_id: ID of the item to update
            updates: Dictionary of fields to update
            
        Returns:
            The updated item or None if not found
        """
        for i, item in enumerate(self.data):
            if item.get('id') == item_id:
                # Update the item
                self.data[i].update(updates)
                # Update timestamp
                self.data[i]['updated_at'] = datetime.now().isoformat()
                self.save()
                return self.data[i]
        return None
    
    def delete(self, item_id: str) -> bool:
        """
        Delete an item by ID.
        
        Args:
            item_id: ID of the item to delete
            
        Returns:
            True if deleted, False if not found
        """
        for i, item in enumerate(self.data):
            if item.get('id') == item_id:
                self.data.pop(i)
                self.save()
                return True
        return False
    
    def clear(self) -> None:
        """Clear all data from the store."""
        self.data = []
        self.save()


# Specialized stores for specific data types

class JobUrlStore(JsonStore):
    """Store for job URLs with additional functionality."""

    def add(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Add a job URL with duplicate prevention and proper prefix generation."""
        url = item.get('url', '').strip()
        if not url:
            raise ValueError("URL is required")

        # Check for duplicate URLs
        existing_url = self.find_by_url(url)
        if existing_url:
            logger.warning(f"URL already exists with prefix {existing_url.get('prefix')}: {url}")
            return existing_url

        # Generate next available prefix
        prefix = self._generate_next_prefix()

        # Set required fields (remove updated_at, keep only created_at)
        item['url'] = url
        item['prefix'] = prefix
        item.setdefault('is_active', True)
        item.setdefault('name', f"Job Source {prefix}")

        # Remove updated_at field - we only need created_at
        if 'updated_at' in item:
            del item['updated_at']

        return super().add(item)

    def find_by_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Find a URL entry by its URL string."""
        url = url.strip()
        for item in self.data:
            if item.get('url', '').strip() == url:
                return item
        return None

    def find_by_prefix(self, prefix: str) -> Optional[Dict[str, Any]]:
        """Find a URL entry by its prefix."""
        for item in self.data:
            if item.get('prefix') == prefix:
                return item
        return None

    def _generate_next_prefix(self) -> str:
        """Generate the next available prefix in sequence (AAA, AAB, AAC, etc.)."""
        existing_prefixes = {item.get('prefix') for item in self.data if item.get('prefix')}

        # Start with AAA and find the next available prefix
        index = 0
        while True:
            prefix = self._generate_prefix(index)
            if prefix not in existing_prefixes:
                return prefix
            index += 1

            # Safety check to prevent infinite loop
            if index > 17576:  # 26^3 = 17576 possible 3-letter combinations
                raise ValueError("No more available prefixes")

    def _generate_prefix(self, index: int) -> str:
        """Generate a three-letter prefix (AAA, AAB, AAC, etc.) from an index."""
        # Ensure index is positive
        index = max(0, index)

        # Convert to base-26 (A-Z) with 3 digits
        first_char = chr(65 + (index // 676) % 26)  # 26^2 = 676
        second_char = chr(65 + (index // 26) % 26)
        third_char = chr(65 + index % 26)

        return f"{first_char}{second_char}{third_char}"

    def deduplicate(self) -> int:
        """Remove duplicate URLs, keeping the first occurrence of each unique URL."""
        seen_urls = set()
        unique_items = []
        removed_count = 0

        for item in self.data:
            url = item.get('url', '').strip()
            if url and url not in seen_urls:
                seen_urls.add(url)
                # Clean up the item: remove updated_at, ensure proper structure
                clean_item = {
                    'url': url,
                    'prefix': item.get('prefix', ''),
                    'is_active': item.get('is_active', True),
                    'name': item.get('name', f"Job Source {item.get('prefix', '')}"),
                    'created_at': item.get('created_at', datetime.now().isoformat())
                }
                # Remove id field - use prefix as unique identifier
                if 'id' in item:
                    clean_item['id'] = item['id']
                unique_items.append(clean_item)
            else:
                removed_count += 1

        # Update data and save
        self.data = unique_items
        self.save()

        logger.info(f"Deduplication complete: removed {removed_count} duplicates, kept {len(unique_items)} unique URLs")
        return removed_count

    def normalize_prefixes(self) -> int:
        """Reassign prefixes in sequential order (AAA, AAB, AAC, etc.) and clean up structure."""
        updated_count = 0

        # Sort by creation date to maintain some consistency
        sorted_items = sorted(self.data, key=lambda x: x.get('created_at', ''))

        for index, item in enumerate(sorted_items):
            new_prefix = self._generate_prefix(index)
            old_prefix = item.get('prefix', '')

            # Clean up the item structure
            clean_item = {
                'url': item.get('url', '').strip(),
                'prefix': new_prefix,
                'is_active': item.get('is_active', True),
                'name': f"Job Source {new_prefix}",
                'created_at': item.get('created_at', datetime.now().isoformat())
            }

            # Update the item
            sorted_items[index] = clean_item

            if old_prefix != new_prefix:
                updated_count += 1
                logger.info(f"Updated prefix: {old_prefix} -> {new_prefix} for {clean_item['url']}")

        # Update data and save
        self.data = sorted_items
        self.save()

        logger.info(f"Prefix normalization complete: updated {updated_count} prefixes")
        return updated_count


class CVStore(JsonStore):
    """Store for CV data with additional functionality."""
    
    def get_summary(self) -> str:
        """Get the CV summary text."""
        if not self.data:
            return ""
        return self.data[0].get('summary', '')
    
    def update_summary(self, summary: str) -> Dict[str, Any]:
        """Update the CV summary."""
        if not self.data:
            # Create new CV entry
            return self.add({'summary': summary})
        
        # Update existing entry
        return self.update(self.data[0]['id'], {'summary': summary})


class ShortlistStore(JsonStore):
    """Store for shortlisted jobs with metadata and versioning."""

    def add_shortlisted_job(self, job: Dict[str, Any], match_score: float, cv_summary: str, temperature: float = 0.7) -> Dict[str, Any]:
        """Add a job to the shortlist with metadata."""
        shortlist_entry = {
            'job_id': job.get('id'),
            'job_data': job.copy(),
            'match_score': round(match_score, 3),
            'confidence_percentage': round(match_score * 100, 1),
            'shortlisted_at': datetime.now().isoformat(),
            'cv_summary_hash': hashlib.md5(cv_summary.encode()).hexdigest()[:8],  # Short hash for CV version tracking
            'temperature_used': temperature,
            'is_active': True
        }

        # Remove any existing entry for this job
        self.remove_job_from_shortlist(job.get('id'))

        return self.add(shortlist_entry)

    def remove_job_from_shortlist(self, job_id: str) -> bool:
        """Remove a job from the shortlist."""
        removed = False
        self.data = [entry for entry in self.data if entry.get('job_id') != job_id]
        removed = True
        if removed:
            self.save()
        return removed

    def get_active_shortlist(self, min_confidence: float = 75.0) -> List[Dict[str, Any]]:
        """Get active shortlisted jobs above minimum confidence level."""
        active_jobs = []
        for entry in self.data:
            if (entry.get('is_active', True) and
                entry.get('confidence_percentage', 0) >= min_confidence):
                active_jobs.append(entry)

        # Sort by confidence percentage (highest first)
        active_jobs.sort(key=lambda x: x.get('confidence_percentage', 0), reverse=True)
        return active_jobs

    def get_shortlist_statistics(self) -> Dict[str, Any]:
        """Get statistics about the shortlist."""
        total_entries = len(self.data)
        active_entries = len([e for e in self.data if e.get('is_active', True)])

        if not self.data:
            return {
                'total_entries': 0,
                'active_entries': 0,
                'average_confidence': 0,
                'last_updated': None
            }

        active_confidences = [e.get('confidence_percentage', 0) for e in self.data if e.get('is_active', True)]
        avg_confidence = sum(active_confidences) / len(active_confidences) if active_confidences else 0

        # Get most recent shortlisting date
        dates = [e.get('shortlisted_at') for e in self.data if e.get('shortlisted_at')]
        last_updated = max(dates) if dates else None

        return {
            'total_entries': total_entries,
            'active_entries': active_entries,
            'average_confidence': round(avg_confidence, 1),
            'last_updated': last_updated
        }

    def refresh_shortlist_for_cv_change(self, new_cv_summary: str) -> bool:
        """
        Check if CV has changed and refresh shortlist if needed.
        Returns True if refresh was performed.
        """
        if not self.data:
            return False

        # Get current CV hash from most recent entry
        current_cv_hash = self.data[0].get('cv_summary_hash', '') if self.data else ''
        new_cv_hash = hashlib.md5(new_cv_summary.encode()).hexdigest()[:8]

        if current_cv_hash != new_cv_hash:
            logger.info(f"CV changed (hash: {current_cv_hash} -> {new_cv_hash}), refreshing shortlist")
            # Mark all current entries as inactive
            for entry in self.data:
                entry['is_active'] = False
                entry['updated_at'] = datetime.now().isoformat()

            self.save()
            return True

        return False

    def get_cv_change_status(self, current_cv_summary: str) -> Dict[str, Any]:
        """
        Get information about CV changes and shortlist status.
        """
        if not self.data:
            return {
                'has_shortlist': False,
                'cv_changed': False,
                'current_cv_hash': hashlib.md5(current_cv_summary.encode()).hexdigest()[:8],
                'shortlist_cv_hash': None,
                'needs_refresh': False
            }

        current_cv_hash = hashlib.md5(current_cv_summary.encode()).hexdigest()[:8]
        shortlist_cv_hash = self.data[0].get('cv_summary_hash', '') if self.data else ''
        cv_changed = current_cv_hash != shortlist_cv_hash

        return {
            'has_shortlist': len(self.data) > 0,
            'cv_changed': cv_changed,
            'current_cv_hash': current_cv_hash,
            'shortlist_cv_hash': shortlist_cv_hash,
            'needs_refresh': cv_changed and len(self.data) > 0,
            'active_entries': len([e for e in self.data if e.get('is_active', True)]),
            'total_entries': len(self.data)
        }


class JobStore(JsonStore):
    """Store for job data with additional functionality."""

    def get_by_source(self, source_prefix: str) -> List[Dict[str, Any]]:
        """Get jobs by source prefix."""
        return [job for job in self.data if job.get('source') == source_prefix]

    def get_source_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for each source URL."""
        # First, get URL to prefix mapping using proper path resolution
        import os
        data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
        url_store = JobUrlStore(os.path.join(data_dir, "job_urls.json"))
        url_to_prefix = {}
        prefix_to_url = {}

        for url_data in url_store.data:
            url = url_data.get('url')
            prefix = url_data.get('prefix')
            if url and prefix:
                url_to_prefix[url] = prefix
                prefix_to_url[prefix] = url

        # Initialize stats for each URL
        stats = {}
        for url in url_to_prefix.keys():
            stats[url] = {
                'scraped_jobs': 0,
                'shortlisted_jobs': 0,
                'last_scraped': None
            }

        # Count only scraped jobs by source prefix and map to URLs
        # Since we cleaned the database, all jobs are now scraped jobs
        for job in self.data:
            source_prefix = job.get('source', 'Unknown')
            url = prefix_to_url.get(source_prefix)

            if url and url in stats:
                # All jobs in the cleaned database have scraped_at field
                stats[url]['scraped_jobs'] += 1

                # Update last scraped time
                scraped_at = job.get('scraped_at')
                if scraped_at and (not stats[url]['last_scraped'] or scraped_at > stats[url]['last_scraped']):
                    stats[url]['last_scraped'] = scraped_at

                # Check if shortlisted
                if job.get('isShortlisted', False):
                    stats[url]['shortlisted_jobs'] += 1

        return stats

    def calculate_job_match_score(self, job: Dict[str, Any], cv_data: Dict[str, Any]) -> float:
        """Calculate match score between job and CV (0.0 to 1.0)."""
        if not cv_data:
            return 0.0

        job_title = job.get('title', '').lower()
        job_description = job.get('description', '').lower()
        job_location = job.get('location', '').lower()

        # Get CV content - our CV data has only a summary field
        cv_summary = cv_data.get('summary', '').lower()
        if not cv_summary:
            return 0.0

        # Extract key terms from CV summary
        cv_words = set(cv_summary.split())

        # Define executive/management keywords that should score highly
        executive_keywords = {
            'executive', 'senior', 'director', 'manager', 'head', 'chief', 'ceo', 'cfo', 'coo',
            'vice', 'president', 'vp', 'lead', 'leader', 'management', 'strategic', 'business',
            'development', 'sales', 'account', 'key', 'international', 'global'
        }

        # Define industry/skill keywords from CV
        cv_skills = {
            'supply', 'chain', 'manufacturing', 'industrial', 'business', 'development',
            'sales', 'strategic', 'turnaround', 'restructuring', 'international', 'global',
            'revenue', 'budget', 'team', 'leadership', 'project', 'management', 'sap', 'crm',
            'lean', 'financial', 'operations', 'acquisition', 'trading'
        }

        score = 0.0

        # Title matching (50% weight) - Most important for executive roles
        title_words = set(job_title.split())

        # High score for executive titles
        executive_title_match = len(title_words & executive_keywords)
        if executive_title_match > 0:
            score += 0.3 * min(executive_title_match / 2, 1.0)  # Cap at 2 keywords

        # Medium score for skill/industry matches in title
        skill_title_match = len(title_words & cv_skills)
        if skill_title_match > 0:
            score += 0.2 * min(skill_title_match / 3, 1.0)  # Cap at 3 keywords

        # Description matching (30% weight)
        if job_description and job_description != 'not specified':
            desc_words = set(job_description.split())

            # Executive keywords in description
            exec_desc_match = len(desc_words & executive_keywords)
            if exec_desc_match > 0:
                score += 0.15 * min(exec_desc_match / 5, 1.0)  # Cap at 5 keywords

            # Skill keywords in description
            skill_desc_match = len(desc_words & cv_skills)
            if skill_desc_match > 0:
                score += 0.15 * min(skill_desc_match / 8, 1.0)  # Cap at 8 keywords

        # General word overlap (20% weight) - Broader matching
        all_job_words = title_words | set(job_description.split()) if job_description else title_words
        common_words = cv_words & all_job_words
        # Filter out very common words
        meaningful_words = common_words - {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must'}

        if meaningful_words and cv_words:
            overlap_ratio = len(meaningful_words) / min(len(cv_words), 50)  # Normalize against CV length (max 50 words)
            score += 0.2 * min(overlap_ratio, 1.0)

        # Ensure score is between 0 and 1
        return min(score, 1.0)

    def _normalize_title(self, title: str) -> str:
        """Normalize job title for better duplicate detection."""
        import re

        # Convert to lowercase and strip
        normalized = title.lower().strip()

        # Remove common variations and formatting
        normalized = re.sub(r'\s*\(m/w/d\)\s*', '', normalized)  # Remove (m/w/d)
        normalized = re.sub(r'\s*\(all genders\)\s*', '', normalized)  # Remove (all genders)
        normalized = re.sub(r'\s*\(w/m/d\)\s*', '', normalized)  # Remove (w/m/d)
        normalized = re.sub(r'\s+', ' ', normalized)  # Normalize whitespace
        normalized = normalized.strip()

        return normalized

    def _deduplicate_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Remove duplicate jobs based on normalized title and source URL.
        Keeps the job with the highest match score if duplicates exist.
        """
        seen_jobs = {}
        unique_jobs = []

        for job in jobs:
            # Create a unique key based on normalized title and source (more reliable than company name)
            title = self._normalize_title(job.get('title', ''))
            source = job.get('source', '').strip()

            # Create composite key for deduplication - use title + source for better accuracy
            job_key = f"{title}|{source}"

            if job_key not in seen_jobs:
                seen_jobs[job_key] = job
                unique_jobs.append(job)
            else:
                # If duplicate found, keep the one with higher match score
                existing_job = seen_jobs[job_key]
                current_score = job.get('match_score', 0)
                existing_score = existing_job.get('match_score', 0)

                if current_score > existing_score:
                    # Replace with higher scoring job
                    seen_jobs[job_key] = job
                    # Find and replace in unique_jobs list
                    for i, unique_job in enumerate(unique_jobs):
                        if unique_job is existing_job:
                            unique_jobs[i] = job
                            break

                logger.info(f"Removed duplicate job: '{job.get('title')}' from source {job.get('source')} (kept score: {max(current_score, existing_score)})")

        return unique_jobs

    def shortlist_jobs_by_cv(self, cv_data: Dict[str, Any], min_score: float = 0.7, max_jobs: int = 20, min_confidence: float = 75.0) -> List[Dict[str, Any]]:
        """
        Shortlist jobs based on CV match score with deduplication and persistence.

        Args:
            cv_data: CV information for matching
            min_score: Minimum match score (temperature level, default 0.7)
            max_jobs: Maximum number of jobs to return
            min_confidence: Minimum confidence percentage to display (default 75%)
        """
        if not cv_data:
            logger.warning("No CV data provided for shortlisting")
            return []

        # Get only real scraped jobs (exclude mock jobs)
        scraped_jobs = self.get_scraped_jobs()
        if not scraped_jobs:
            logger.warning("No scraped jobs found for shortlisting")
            return []

        cv_summary = cv_data.get('summary', '')
        if not cv_summary:
            logger.warning("No CV summary found for matching")
            return []

        # Calculate scores for scraped jobs only
        scored_jobs = []
        for job in scraped_jobs:
            score = self.calculate_job_match_score(job, cv_data)
            if score >= min_score:
                job_copy = job.copy()
                job_copy['match_score'] = round(score, 3)
                job_copy['confidence_percentage'] = round(score * 100, 1)
                job_copy['isShortlisted'] = True
                scored_jobs.append(job_copy)

        # Remove duplicates before sorting and limiting
        unique_jobs = self._deduplicate_jobs(scored_jobs)

        # Sort by score (highest first) and limit results
        unique_jobs.sort(key=lambda x: x['match_score'], reverse=True)

        # Filter by minimum confidence percentage
        filtered_jobs = [job for job in unique_jobs if job.get('confidence_percentage', 0) >= min_confidence]
        shortlisted = filtered_jobs[:max_jobs]

        # Persist shortlisted jobs to dedicated shortlist store
        self._persist_shortlisted_jobs(shortlisted, cv_summary, min_score)

        logger.info(f"Shortlisted {len(shortlisted)} unique jobs from {len(scraped_jobs)} scraped jobs (removed {len(scored_jobs) - len(unique_jobs)} duplicates, min_score: {min_score}, min_confidence: {min_confidence}%)")
        return shortlisted

    def _persist_shortlisted_jobs(self, shortlisted_jobs: List[Dict[str, Any]], cv_summary: str, temperature: float):
        """Persist shortlisted jobs to the dedicated shortlist store."""
        try:
            # Import here to avoid circular imports
            import os
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
            shortlist_store = ShortlistStore(os.path.join(data_dir, "shortlist.json"))

            # Clear existing shortlist and add new entries
            shortlist_store.data = []

            for job in shortlisted_jobs:
                shortlist_store.add_shortlisted_job(
                    job=job,
                    match_score=job.get('match_score', 0),
                    cv_summary=cv_summary,
                    temperature=temperature
                )

            logger.info(f"Persisted {len(shortlisted_jobs)} jobs to shortlist.json")

        except Exception as e:
            logger.error(f"Failed to persist shortlisted jobs: {str(e)}")
            # Don't fail the shortlisting process if persistence fails

    def get_shortlisted(self) -> List[Dict[str, Any]]:
        """Get shortlisted jobs."""
        return [job for job in self.data if job.get('isShortlisted', False)]

    def get_scraped_jobs(self) -> List[Dict[str, Any]]:
        """Get only real scraped jobs (not mock jobs)."""
        return [job for job in self.data if 'scraped_at' in job]

    def get_mock_jobs(self) -> List[Dict[str, Any]]:
        """Get only mock jobs (no scraped_at field)."""
        return [job for job in self.data if 'scraped_at' not in job]

    def remove_mock_jobs(self) -> int:
        """Remove all mock jobs from the store."""
        mock_jobs = self.get_mock_jobs()
        mock_count = len(mock_jobs)

        if mock_count > 0:
            # Keep only scraped jobs
            self.data = self.get_scraped_jobs()
            self.save()
            logger.info(f"Removed {mock_count} mock jobs, kept {len(self.data)} real scraped jobs")

        return mock_count

    def get_last_scrape_date(self) -> Optional[str]:
        """Get the date of the last scraping operation (YYYY-MM-DD format)."""
        scraped_jobs = self.get_scraped_jobs()
        if not scraped_jobs:
            return None

        # Find the most recent scraped_at timestamp
        latest_scraped_at = max(job['scraped_at'] for job in scraped_jobs)
        # Extract just the date part (YYYY-MM-DD)
        return latest_scraped_at.split('T')[0]

    def was_scraped_today(self) -> bool:
        """Check if scraping was performed today."""
        last_scrape_date = self.get_last_scrape_date()
        if not last_scrape_date:
            return False

        today = datetime.now().strftime('%Y-%m-%d')
        return last_scrape_date == today

    def find_duplicate_by_title(self, title: str, source_prefix: str = None) -> Optional[Dict[str, Any]]:
        """
        Find a job with the same title (case-insensitive).

        Args:
            title: Job title to search for
            source_prefix: Optional source prefix to limit search

        Returns:
            Existing job with same title or None
        """
        title_lower = title.lower().strip()

        for job in self.data:
            # Skip if source filter is specified and doesn't match
            if source_prefix and job.get('source') != source_prefix:
                continue

            existing_title = job.get('title', '').lower().strip()
            if existing_title == title_lower:
                return job

        return None

    def add_if_not_duplicate(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a job only if no duplicate title exists.

        Args:
            item: The job item to add

        Returns:
            The added item, existing duplicate, or None if skipped
        """
        title = item.get('title', '')
        source_prefix = item.get('source', '')

        # Check for duplicate by title
        existing_job = self.find_duplicate_by_title(title, source_prefix)
        if existing_job:
            logger.info(f"Skipping duplicate job: '{title}' (ID: {existing_job.get('id')})")
            return existing_job

        # No duplicate found, add the job
        return self.add(item)

    def get_scraping_stats(self) -> Dict[str, Any]:
        """Get statistics about scraped jobs."""
        scraped_jobs = self.get_scraped_jobs()
        mock_jobs = [job for job in self.data if 'scraped_at' not in job]

        stats = {
            'total_jobs': len(self.data),
            'scraped_jobs': len(scraped_jobs),
            'mock_jobs': len(mock_jobs),
            'last_scrape_date': self.get_last_scrape_date(),
            'was_scraped_today': self.was_scraped_today()
        }

        if scraped_jobs:
            # Count jobs by source
            source_counts = {}
            for job in scraped_jobs:
                source = job.get('source', 'Unknown')
                source_counts[source] = source_counts.get(source, 0) + 1
            stats['jobs_by_source'] = source_counts

        return stats
