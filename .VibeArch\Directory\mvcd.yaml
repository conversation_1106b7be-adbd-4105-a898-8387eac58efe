codebase:
- file: backend/api.py
  element: add_log_message
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - asyncio
  - concurrent
  - fastapi
  - logging
  - queue
  - scraper
  - storage
  - threading
  - uvicorn
  loc: 551
  last_modified: 1752046018.7195783
- file: backend/scraper.py
  element: JobScraper
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - asyncio
  - dotenv
  - firecrawl
  - logging
  - openai
  - signal
  loc: 233
  last_modified: 1751889632.1669087
- file: backend/storage.py
  element: JsonStore
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - hashlib
  - logging
  loc: 462
  last_modified: 1752045993.174485
- file: clean_mock_jobs.py
  element: main
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - storage
  loc: 46
  last_modified: 1751953766.35803
- file: test_single_crawl.py
  element: test_single_url
  type: Other
  description: 'TODO: Add description'
  confidence: 0
  status: active
  dependencies:
  - scraper
  loc: 32
  last_modified: 1748282276.5997152
