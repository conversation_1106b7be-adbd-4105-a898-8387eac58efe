# JoMaDe Development Guidelines
p
## Project Overview
- jomade is a FAB (frontend/api/backend) structure of the jobscraper, which is a searchtool for the scattered jobmarket
- the system scrape a list of urls for job titles and makes a total list
- the system match a specific CV and CV summary with the job titles and make a shortlist
- based on the shortlist the system matches the cv with the job description
- feel free to make suggestions where the LLM, ai agents and Model Context Protocol (MCP)can be used

## Core Principles
- Deal with root causes, not symptoms - never use hardcoded fallbacks, validations, placeholders, or workarounds
- Never hardcode variables which should be delivered by the LLM
- When debugging, first present an analysis and wait for user feedback
- This application runs on Windows using PowerShell
- when adding code which is replacing other code, the old code has to be marked as deprecated and obsolete
- we keep and maintain one startup script: run.ps1 ONLY
- we shall have and maintain one .env file in the root -  maintains a single source of truth for all environments

You are an expert in Python, TypeScript, React, FastAPI, JSON-based APIs, prompt engineering, and modern frontend-backend architecture.

- frequently update 20250515_Development_Strategy.md

# Dependency Management
- Always manage Python dependencies via `pip freeze > requirements.txt` and use a virtual environment.
- Remove unused packages to maintain lean requirements.
- Check for outdated packages using `pip list --outdated`, and validate installs with `pip check`.
- Use version pinning (e.g., `fastapi==0.100.1`) to avoid breaking changes.
- Automate updates using Dependabot or `pip-tools` (`pip-compile`).
- Before modifying `package.json`, review `dependency-notes.md`.

# Prompt Engineering
- Do NOT hardcode LLM prompts in source code.
- Store all prompts as YAML files in a Prompt_library
- - LLM calls must ONLY be made from the backend (never frontend).
- Do not change prompts unless clearly instructed by the user.

# Terminal Commands
- - Do not suggest commands for Mac or Linux — Windows only.

# Coding Patterns and Structure
- Prioritize simple, maintainable solutions.
- Avoid hardcoded defaults and duplicated logic.
- Avoid files longer than 300 lines—modularize when expanding.
- Never mock or stub data in dev/prod—mocking is test-only.
- Never mask real error messages—report root causes directly.
- Do not overwrite `.env` files without confirmation from the user.
- Before adding new functionality, check for existing files with similar responsibilities.
- Reflect when the user reflects, and always ask if suggestions should be implemented.

# Code Style and Communication
- Do not use emojis unless explicitly asked.
- Use TypeScript interfaces (not types), avoid enums, prefer RORO pattern.
- Always communicate decisions and ask for confirmation on implementation.
