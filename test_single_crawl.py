#!/usr/bin/env python3
"""
Test script to crawl a single URL and see detailed extraction results.
"""

import sys
import os
sys.path.append('backend')

from scraper import create_scraper

def test_single_url():
    """Test crawling a single URL with detailed logging."""
    
    # Test URL - use the first one from your list
    test_url = "https://careers.eoexecutives.com/"
    test_prefix = "TEST"
    
    print("🧪 TESTING SINGLE URL CRAWL")
    print(f"URL: {test_url}")
    print(f"Prefix: {test_prefix}")
    print("=" * 50)
    
    # Create scraper
    scraper = create_scraper()
    if not scraper:
        print("❌ Failed to create scraper - check API keys")
        return
    
    # Test crawling
    print("\n🕷️ Starting crawl...")
    jobs = scraper.crawl_url(test_url, test_prefix)
    
    print(f"\n🎯 RESULTS:")
    print(f"   Jobs found: {len(jobs)}")
    
    if jobs:
        print(f"\n📋 First few jobs:")
        for i, job in enumerate(jobs[:3]):
            print(f"   {i+1}. {job['title']} at {job['company']} ({job['location']})")
            print(f"      Link: {job['link']}")
            print(f"      Description: {job['description'][:100]}...")
            print()
    else:
        print("   ❌ No jobs extracted")
    
    print("=" * 50)
    print("🧪 Test complete")

if __name__ == "__main__":
    test_single_url()
