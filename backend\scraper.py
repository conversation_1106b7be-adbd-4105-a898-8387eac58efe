"""
Simple Firecrawl-based job scraper for JoMaDe application.
This module handles scraping job URLs using Firecrawl API and extracting job information using OpenAI.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import re
import asyncio
import signal
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, TimeoutError as FuturesTimeoutError

from firecrawl import FirecrawlApp
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JobScraper:
    """Simple job scraper using Firecrawl and OpenAI."""

    def __init__(self, timeout_seconds: int = 300):
        """Initialize the scraper with API keys from environment.

        Args:
            timeout_seconds: Maximum time to wait for each URL scraping operation (default: 5 minutes)
        """
        self.firecrawl_api_key = os.getenv('FIRECRAWL_API_KEY')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.timeout_seconds = timeout_seconds

        if not self.firecrawl_api_key:
            raise ValueError("FIRECRAWL_API_KEY not found in environment variables")
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")

        # Initialize Firecrawl
        self.firecrawl = FirecrawlApp(api_key=self.firecrawl_api_key)

        # Initialize OpenAI
        openai.api_key = self.openai_api_key
        self.openai_client = openai.OpenAI(api_key=self.openai_api_key)

        logger.info(f"JobScraper initialized successfully with {timeout_seconds}s timeout")

    def _crawl_url_with_timeout(self, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """
        Internal method to crawl URL with timeout handling.
        This runs the actual crawling logic that can be interrupted.
        """
        return self._crawl_url_internal(url, source_prefix, log_callback)

    def crawl_url(self, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """
        Crawl a website for job listings using Firecrawl with timeout handling.

        Args:
            url: The URL to crawl
            source_prefix: The three-letter prefix for this source (e.g., AAA)
            log_callback: Optional callback function for real-time logging

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        try:
            log("info", f"🕷️ CRAWLING: {url} (prefix: {source_prefix}) - Timeout: {self.timeout_seconds}s")
            logger.info(f"Starting crawl for URL: {url} with prefix: {source_prefix}, timeout: {self.timeout_seconds}s")

            # Use ThreadPoolExecutor with timeout to prevent hanging
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(self._crawl_url_with_timeout, url, source_prefix, log_callback)
                try:
                    result = future.result(timeout=self.timeout_seconds)
                    return result
                except FuturesTimeoutError:
                    log("error", f"⏰ TIMEOUT: Crawling {url} exceeded {self.timeout_seconds} seconds")
                    logger.warning(f"Crawling {url} timed out after {self.timeout_seconds} seconds")
                    return []
                except Exception as e:
                    log("error", f"❌ ERROR in timeout wrapper for {url}: {str(e)}")
                    logger.error(f"Error in timeout wrapper for {url}: {str(e)}")
                    return []

        except Exception as e:
            log("error", f"❌ ERROR setting up crawl for {url}: {str(e)}")
            logger.error(f"Error setting up crawl for {url}: {str(e)}")
            return []

    def _crawl_url_internal(self, url: str, source_prefix: str, log_callback=None) -> List[Dict[str, Any]]:
        """
        Internal crawling method that performs the actual Firecrawl API calls.

        Args:
            url: The URL to crawl
            source_prefix: The three-letter prefix for this source (e.g., AAA)
            log_callback: Optional callback function for real-time logging

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        try:
            log("info", f"📡 Calling Firecrawl CRAWL API for {url}...")
            from firecrawl import ScrapeOptions

            # Reduce the limit to prevent excessive crawling that could cause timeouts
            crawl_result = self.firecrawl.crawl_url(
                url,
                limit=10,  # Reduced from 50 to 10 pages to prevent timeouts
                scrape_options=ScrapeOptions(
                    formats=['markdown'],
                    only_main_content=True
                )
            )

            # Handle Firecrawl Python SDK response structure
            if not crawl_result:
                log("error", f"❌ No response from Firecrawl API for {url}")
                logger.warning(f"No response from Firecrawl API for {url}")
                return []

            # The Firecrawl Python SDK returns the data directly as a list
            # Check if crawl_result is already the data list or if it has a data attribute
            if isinstance(crawl_result, list):
                pages_data = crawl_result
            elif hasattr(crawl_result, 'data') and isinstance(crawl_result.data, list):
                pages_data = crawl_result.data
            elif isinstance(crawl_result, dict) and 'data' in crawl_result:
                pages_data = crawl_result['data']
            else:
                log("error", f"❌ Unexpected response format from Firecrawl API for {url}")
                log("error", f"Response type: {type(crawl_result)}")
                if hasattr(crawl_result, '__dict__'):
                    log("error", f"Response attributes: {list(crawl_result.__dict__.keys())}")
                else:
                    log("error", f"Response dir: {[attr for attr in dir(crawl_result) if not attr.startswith('_')]}")
                logger.warning(f"Unexpected response format from Firecrawl API for {url}")
                return []

            if not pages_data:
                log("error", f"❌ No content crawled from {url}")
                logger.warning(f"No content crawled from {url}")
                return []
            log("success", f"✅ Crawled {len(pages_data)} pages from {url}")
            logger.info(f"Crawled {len(pages_data)} pages from {url}")

            # Process all crawled pages to extract jobs
            all_jobs = []
            for i, page in enumerate(pages_data):
                # Handle different page data structures from Firecrawl SDK
                page_markdown = None
                page_url = url

                # Current Firecrawl SDK returns dict with 'markdown' and 'metadata' keys
                if isinstance(page, dict):
                    page_markdown = page.get('markdown')
                    if page.get('metadata') and isinstance(page['metadata'], dict):
                        page_url = page['metadata'].get('sourceURL', url)
                # Fallback for object-based responses
                elif hasattr(page, 'markdown'):
                    page_markdown = page.markdown
                    if hasattr(page, 'metadata'):
                        if hasattr(page.metadata, 'sourceURL'):
                            page_url = page.metadata.sourceURL
                        elif isinstance(page.metadata, dict):
                            page_url = page.metadata.get('sourceURL', url)

                if page_markdown:
                    log("info", f"🔍 Processing page {i+1}/{len(pages_data)}: {page_url}")

                    # Extract job information using OpenAI
                    jobs = self._extract_jobs_with_llm(
                        page_markdown,
                        page_url,
                        source_prefix,
                        page_number=i+1,
                        log_callback=log_callback
                    )

                    if jobs:
                        all_jobs.extend(jobs)
                        log("success", f"  ✅ Found {len(jobs)} jobs on this page")
                else:
                    log("warning", f"⚠️  Page {i+1} has no markdown content, skipping")
                    # Debug: show what we actually got
                    if isinstance(page, dict):
                        log("warning", f"     Available keys: {list(page.keys())}")
                    else:
                        log("warning", f"     Page type: {type(page)}, attributes: {[attr for attr in dir(page) if not attr.startswith('_')]}")

            log("success", f"🎯 TOTAL: Extracted {len(all_jobs)} jobs from {url}")
            logger.info(f"Successfully extracted {len(all_jobs)} jobs from {url}")
            return all_jobs

        except Exception as e:
            log("error", f"❌ ERROR crawling {url}: {str(e)}")
            logger.error(f"Error crawling {url}: {str(e)}")
            return []

    def _extract_jobs_with_llm(self, content: str, source_url: str, source_prefix: str, page_number: int = 1, log_callback=None) -> List[Dict[str, Any]]:
        """
        Extract job information from scraped content using OpenAI.

        Args:
            content: The scraped markdown content
            source_url: The original URL
            source_prefix: The three-letter prefix for this source
            page_number: The page number being processed
            log_callback: Optional callback function for real-time logging

        Returns:
            List of job dictionaries
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)
        try:
            # Generic job extraction prompt - works with any website structure
            prompt = f"""
            Extract ALL job postings from this content. Look for ANY pattern that indicates a job listing:
            - Job titles, position names, role names
            - Company names or hiring organizations
            - Locations, cities, countries, "remote"
            - Links to job details or applications
            - Any text that looks like a job posting

            IMPORTANT: Different websites structure job data differently. Be flexible and extract jobs even if the format is unusual.

            For each job found, return:
            {{
                "title": "exact job title found",
                "company": "company name if found, otherwise 'Not specified'",
                "location": "location if found, otherwise 'Not specified'",
                "summary": "brief summary or first sentence of job description (max 150 chars)",
                "detail_url": "direct job URL if found, otherwise '{source_url}'"
            }}

            Return ONLY a valid JSON array. Extract even partial information - better to have incomplete jobs than miss them.

            Content:
            {content[:8000]}
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",  # Better model for extraction
                messages=[
                    {"role": "system", "content": "You are a job listing extraction expert. You MUST return valid JSON array format. Extract ALL job postings from the content, even if information is incomplete."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,  # More tokens for better extraction
                temperature=0.0   # Deterministic output
            )

            # Parse the response
            response_text = response.choices[0].message.content.strip()
            log("info", f"🤖 LLM Response length: {len(response_text)} characters")

            # Try to extract JSON from the response
            import json
            import re

            jobs_data = []
            try:
                # First try: direct JSON parsing
                jobs_data = json.loads(response_text)
                log("success", f"✅ Direct JSON parsing successful: {len(jobs_data)} jobs")
            except json.JSONDecodeError:
                try:
                    # Second try: extract JSON array from response
                    json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                    if json_match:
                        jobs_data = json.loads(json_match.group())
                        log("success", f"✅ Regex JSON extraction successful: {len(jobs_data)} jobs")
                    else:
                        log("error", "❌ No JSON array found in LLM response")
                        log("error", f"Response preview: {response_text[:500]}...")
                        return []
                except json.JSONDecodeError as e:
                    log("error", f"❌ JSON parsing failed: {str(e)}")
                    log("error", f"Response preview: {response_text[:500]}...")
                    return []

            # Process and format the jobs
            jobs = []
            for i, job_data in enumerate(jobs_data[:20]):  # Limit to 20 jobs per page
                job_id = f"{source_prefix}{page_number:02d}{i+1:02d}"  # e.g., AAA0101, AAA0102

                job = {
                    "id": job_id,
                    "title": job_data.get("title", "Unknown Position"),
                    "company": job_data.get("company", "Not specified"),
                    "location": job_data.get("location", "Not specified"),
                    "description": job_data.get("summary", job_data.get("description", "No description available"))[:200],
                    "source": source_prefix,
                    "link": job_data.get("detail_url", job_data.get("link", source_url)),
                    "isShortlisted": False,
                    "scraped_at": datetime.now().isoformat()
                }
                jobs.append(job)

            return jobs

        except Exception as e:
            logger.error(f"Error extracting jobs with LLM: {str(e)}")
            return []

    def scrape_multiple_urls(self, urls_with_prefixes: List[Dict[str, str]], log_callback=None) -> Dict[str, Any]:
        """
        Scrape multiple URLs for job listings.

        Args:
            urls_with_prefixes: List of dicts with 'url' and 'prefix' keys
            log_callback: Optional callback function for real-time logging

        Returns:
            Dictionary with scraping results
        """
        def log(msg_type, message):
            print(message)  # Keep console logging
            if log_callback:
                log_callback(msg_type, message)

        log("info", f"🚀 STARTING BATCH SCRAPING: {len(urls_with_prefixes)} URLs")
        logger.info(f"Starting batch scraping of {len(urls_with_prefixes)} URLs")

        all_jobs = []
        successful_urls = 0
        failed_urls = []

        for i, url_data in enumerate(urls_with_prefixes, 1):
            url = url_data.get('url', '')
            prefix = url_data.get('prefix', 'AAA')

            if not url:
                log("warning", f"⚠️  Skipping empty URL at position {i}")
                continue

            log("info", f"📋 Processing {i}/{len(urls_with_prefixes)}: {prefix}")
            log("info", f"🔗 URL: {url}")

            # Add progress information to the log callback
            if log_callback:
                log_callback("progress", f"Processing {i}/{len(urls_with_prefixes)}: {prefix}", {
                    "current_index": i,
                    "total_urls": len(urls_with_prefixes),
                    "current_prefix": prefix,
                    "current_url": url,
                    "completed_urls": i - 1,
                    "successful_urls": successful_urls,
                    "failed_urls": len(failed_urls)
                })

            jobs = self.crawl_url(url, prefix, log_callback)
            if jobs:
                all_jobs.extend(jobs)
                successful_urls += 1
                log("success", f"✅ Success: {len(jobs)} jobs added (Total: {len(all_jobs)})")
                if log_callback:
                    log_callback("success", f"✅ Success: {len(jobs)} jobs added (Total: {len(all_jobs)})", {
                        "jobs_found": len(jobs),
                        "total_jobs": len(all_jobs),
                        "successful_urls": successful_urls,
                        "current_prefix": prefix
                    })
            else:
                failed_urls.append(url)
                log("error", f"❌ Failed: No jobs found")
                if log_callback:
                    log_callback("error", f"❌ Failed: No jobs found", {
                        "failed_urls": len(failed_urls) + 1,
                        "current_prefix": prefix
                    })

        log("info", f"🎯 SCRAPING COMPLETE:")
        log("info", f"   📊 Total Jobs: {len(all_jobs)}")
        log("info", f"   ✅ Successful URLs: {successful_urls}/{len(urls_with_prefixes)}")
        if failed_urls:
            log("warning", f"   ❌ Failed URLs: {len(failed_urls)}")

        result = {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "url_count": len(urls_with_prefixes),
            "successful_urls": successful_urls,
            "failed_urls": failed_urls,
            "job_count": len(all_jobs),
            "jobs": all_jobs,
            "message": f"Scraped {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs"
        }

        logger.info(f"Batch scraping completed: {len(all_jobs)} jobs from {successful_urls}/{len(urls_with_prefixes)} URLs")
        return result


def create_scraper(timeout_seconds: int = 180) -> Optional[JobScraper]:
    """
    Factory function to create a JobScraper instance.
    Returns None if API keys are not configured.

    Args:
        timeout_seconds: Maximum time to wait for each URL scraping operation (default: 3 minutes)
    """
    try:
        print(f"🔧 Initializing Firecrawl scraper with {timeout_seconds}s timeout...")
        scraper = JobScraper(timeout_seconds=timeout_seconds)
        print("✅ Firecrawl scraper initialized successfully!")
        return scraper
    except ValueError as e:
        print(f"❌ Failed to create scraper: {str(e)}")
        print("🔄 Will use mock scraping instead")
        logger.error(f"Failed to create scraper: {str(e)}")
        return None
